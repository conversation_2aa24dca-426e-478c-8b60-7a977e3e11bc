import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_points_history_screen.dart';
import 'package:culture_connect/screens/loyalty/loyalty_rewards_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_points_history.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_rewards_list.dart';
import 'package:culture_connect/widgets/loyalty/loyalty_tier_card.dart';

/// A screen that displays the loyalty dashboard
class LoyaltyDashboardScreen extends ConsumerWidget {
  /// Creates a new loyalty dashboard screen
  const LoyaltyDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loyaltyProgramAsync = ref.watch(loyaltyProgramProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Loyalty Program',
        showBackButton: true,
      ),
      body: loyaltyProgramAsync.when(
        data: (loyaltyProgram) {
          if (loyaltyProgram == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.card_membership,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No loyalty program found',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Please try again later',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Loyalty tier card
                LoyaltyTierCard(
                  loyaltyProgram: loyaltyProgram,
                  showProgressBar: true,
                  showNextTier: true,
                  showBenefits: false,
                  onTap: () =>
                      _showTierDetailsBottomSheet(context, loyaltyProgram),
                ),

                const SizedBox(height: 24),

                // Quick actions
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),

                const SizedBox(height: 16),

                // Quick action buttons
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'View Rewards',
                        Icons.card_giftcard,
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoyaltyRewardsScreen(
                              loyaltyProgram: loyaltyProgram,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Points History',
                        Icons.history,
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoyaltyPointsHistoryScreen(
                              loyaltyProgram: loyaltyProgram,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildQuickActionButton(
                        context,
                        'Tier Benefits',
                        Icons.stars,
                        () => _showTierDetailsBottomSheet(
                            context, loyaltyProgram),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Points history
                LoyaltyPointsHistory(
                  maxTransactions: 3,
                  showSeeAllButton: true,
                  onSeeAllTapped: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoyaltyPointsHistoryScreen(
                        loyaltyProgram: loyaltyProgram,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Available rewards
                LoyaltyRewardsList(
                  loyaltyProgram: loyaltyProgram,
                  maxRewards: 3,
                  showSeeAllButton: true,
                  onSeeAllTapped: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoyaltyRewardsScreen(
                        loyaltyProgram: loyaltyProgram,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(loyaltyProgramProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showTierDetailsBottomSheet(
      BuildContext context, LoyaltyProgramModel loyaltyProgram) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Title
              const Text(
                'Loyalty Tiers & Benefits',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 24),

              // Current tier
              const Text(
                'Your Current Tier',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 8),

              // Current tier card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: loyaltyProgram.tier.color.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: loyaltyProgram.tier.color,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      loyaltyProgram.tier.icon,
                      size: 32,
                      color: loyaltyProgram.tier.color,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            loyaltyProgram.tier.displayName,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: loyaltyProgram.tier.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${loyaltyProgram.lifetimePoints} lifetime points',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Current tier benefits
              const Text(
                'Your Benefits',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 8),

              // Benefits list
              ...loyaltyProgram.tier.benefits.map((benefit) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.check_circle,
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            benefit,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),

              const SizedBox(height: 24),

              // All tiers
              const Text(
                'All Loyalty Tiers',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 16),

              // Tiers list
              ...LoyaltyTier.values.map((tier) => _buildTierItem(
                    context,
                    tier,
                    isCurrentTier: tier == loyaltyProgram.tier,
                  )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTierItem(
    BuildContext context,
    LoyaltyTier tier, {
    bool isCurrentTier = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCurrentTier ? tier.color.withAlpha(26) : Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentTier ? tier.color : Colors.grey[300]!,
          width: isCurrentTier ? 1 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tier header
          Row(
            children: [
              Icon(
                tier.icon,
                size: 24,
                color: tier.color,
              ),
              const SizedBox(width: 8),
              Text(
                tier.displayName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: tier.color,
                ),
              ),
              const Spacer(),
              Text(
                '${tier.pointsRequired}+ points',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Tier benefits
          const Text(
            'Benefits:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 4),

          ...tier.benefits.take(3).map((benefit) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 14,
                      color: isCurrentTier
                          ? tier.color
                          : AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        benefit,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              )),

          if (tier.benefits.length > 3) ...[
            const SizedBox(height: 4),
            Text(
              '+ ${tier.benefits.length - 3} more benefits',
              style: const TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
