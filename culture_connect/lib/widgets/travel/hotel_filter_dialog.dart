import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/hotel_filter.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/hotel_filter_provider.dart';

/// A dialog for filtering hotels
class HotelFilterDialog extends ConsumerStatefulWidget {
  /// Creates a new hotel filter dialog
  const HotelFilterDialog({super.key});

  @override
  ConsumerState<HotelFilterDialog> createState() => _HotelFilterDialogState();
}

class _HotelFilterDialogState extends ConsumerState<HotelFilterDialog> {
  late HotelFilter _filter;
  final double _maxPrice = 1000.0;

  @override
  void initState() {
    super.initState();
    _filter = ref.read(hotelFilterProvider);
  }

  void _applyFilters() {
    ref.read(hotelFilterProvider.notifier).state = _filter;
    Navigator.of(context).pop();
  }

  void _resetFilters() {
    setState(() {
      _filter = _filter.reset();
    });
  }

  Future<void> _selectCheckInDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _filter.checkInDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _filter = _filter.copyWith(checkInDate: picked);

        // If checkout date is before checkin date, update it
        if (_filter.checkOutDate != null &&
            _filter.checkOutDate!.isBefore(picked)) {
          _filter = _filter.copyWith(
              checkOutDate: picked.add(const Duration(days: 1)));
        }
      });
    }
  }

  Future<void> _selectCheckOutDate() async {
    if (_filter.checkInDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a check-in date first'),
        ),
      );
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _filter.checkOutDate ??
          _filter.checkInDate!.add(const Duration(days: 1)),
      firstDate: _filter.checkInDate!,
      lastDate: _filter.checkInDate!.add(const Duration(days: 30)),
    );

    if (picked != null) {
      setState(() {
        _filter = _filter.copyWith(checkOutDate: picked);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Filter Hotels',
                      style: theme.textTheme.titleLarge,
                    ),
                  ),
                  TextButton(
                    onPressed: _resetFilters,
                    child: const Text('Reset'),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),

            const Divider(),

            // Filter options
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Star rating filter
                  Text(
                    'Star Rating',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: HotelStarRating.values.map((rating) {
                      final isSelected =
                          _filter.starRatings?.contains(rating) ?? false;
                      return FilterChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(_getStarRatingText(rating)),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.star,
                              size: 16,
                              color: isSelected ? Colors.white : Colors.amber,
                            ),
                          ],
                        ),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            final starRatings =
                                _filter.starRatings?.toList() ?? [];
                            if (selected) {
                              starRatings.add(rating);
                            } else {
                              starRatings.remove(rating);
                            }
                            _filter =
                                _filter.copyWith(starRatings: starRatings);
                          });
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // Price range filter
                  Text(
                    'Price Range (per night)',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  RangeSlider(
                    values: _filter.priceRange ?? const RangeValues(0, 1000),
                    min: 0,
                    max: _maxPrice,
                    divisions: 50,
                    labels: RangeLabels(
                      '\$${(_filter.priceRange?.start ?? 0).round()}',
                      '\$${(_filter.priceRange?.end ?? _maxPrice).round()}',
                    ),
                    onChanged: (values) {
                      setState(() {
                        _filter = _filter.copyWith(priceRange: values);
                      });
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('\$0'),
                        Text('\$${_maxPrice.round()}'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Rating filter
                  Text(
                    'Minimum Rating',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: _filter.minRating ?? 0,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    label: (_filter.minRating ?? 0).toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() {
                        _filter = _filter.copyWith(minRating: value);
                      });
                    },
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Any'),
                        Row(
                          children: [
                            Text('5.0'),
                            SizedBox(width: 4),
                            Icon(Icons.star, size: 16, color: Colors.amber),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Date filter
                  Text(
                    'Stay Dates',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: _selectCheckInDate,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Check-in Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.all(12),
                            ),
                            child: Text(
                              _filter.checkInDate != null
                                  ? DateFormat('MMM dd, yyyy')
                                      .format(_filter.checkInDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: _selectCheckOutDate,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              labelText: 'Check-out Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.all(12),
                            ),
                            child: Text(
                              _filter.checkOutDate != null
                                  ? DateFormat('MMM dd, yyyy')
                                      .format(_filter.checkOutDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Location filter
                  Text(
                    'Location',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    decoration: InputDecoration(
                      labelText: 'Search by location',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.search),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _filter = _filter.copyWith(locationQuery: value);
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Minimum guests filter
                  Text(
                    'Minimum Guests',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: (_filter.minGuests ?? 1).toDouble(),
                    min: 1,
                    max: 10,
                    divisions: 9,
                    label: '${_filter.minGuests ?? 1}',
                    onChanged: (value) {
                      setState(() {
                        _filter = _filter.copyWith(minGuests: value.round());
                      });
                    },
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('1 guest'),
                        Text('10 guests'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Amenities filter
                  Text(
                    'Amenities',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  _buildAmenityCheckbox('Restaurant', 'restaurant'),
                  _buildAmenityCheckbox('Bar', 'bar'),
                  _buildAmenityCheckbox('Swimming Pool', 'pool'),
                  _buildAmenityCheckbox('Spa', 'spa'),
                  _buildAmenityCheckbox('Gym', 'gym'),
                  _buildAmenityCheckbox('Free WiFi', 'freeWifi'),
                  _buildAmenityCheckbox('Free Parking', 'freeParking'),
                  _buildAmenityCheckbox('Room Service', 'roomService'),
                  _buildAmenityCheckbox('Business Center', 'businessCenter'),
                  _buildAmenityCheckbox('Kids Club', 'kidsClub'),
                ],
              ),
            ),

            const Divider(),

            // Footer
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _applyFilters,
                    child: const Text('Apply Filters'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmenityCheckbox(String label, String amenity) {
    final amenities = _filter.amenities ?? {};
    final isChecked = amenities[amenity] ?? false;

    return CheckboxListTile(
      title: Text(label),
      value: isChecked,
      onChanged: (value) {
        setState(() {
          final newAmenities = Map<String, bool>.from(amenities);
          newAmenities[amenity] = value ?? false;
          _filter = _filter.copyWith(amenities: newAmenities);
        });
      },
      controlAffinity: ListTileControlAffinity.leading,
      dense: true,
    );
  }

  String _getStarRatingText(HotelStarRating rating) {
    switch (rating) {
      case HotelStarRating.oneStar:
        return '1';
      case HotelStarRating.twoStar:
        return '2';
      case HotelStarRating.threeStar:
        return '3';
      case HotelStarRating.fourStar:
        return '4';
      case HotelStarRating.fiveStar:
        return '5';
    }
  }
}
